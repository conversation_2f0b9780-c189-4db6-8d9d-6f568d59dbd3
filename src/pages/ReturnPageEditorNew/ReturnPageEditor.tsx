import '@aftership/atta-engine/style';

import {
    AttaEditor,
    AttaEditorProvider,
    messageChannel,
} from '@aftership/atta-engine/editor';
import { EventName, UpdateViewType } from '@aftership/preview-kit/business/rc';
import { MessageEventType as EventType } from '@aftership/preview-kit/host';
import FullScreenWorkshop, {
    OwnProps,
} from 'components/FullScreenWorkshop/FullScreenWorkshop';
import { useFormikContext } from 'formik';
import {
    generateReturnPageUrl,
    useQueryPageUrls,
} from 'hooks/useQueryPageUrls';
import {
    Client,
    PageEditorFormValues,
} from 'pages/ReturnPageEditor/components/types/content';
import { previewSectionNameMap } from 'pages/ReturnPageEditor/constants';
import {
    usePreviewI18nResource,
    usePreviewMessageV2,
} from 'pages/ReturnPageEditor/hooks/useMessage';
import { usePageEditorContext } from 'pages/ReturnPageEditor/PageEditorContext';
import { useCallback, useEffect, useState } from 'react';
import { RouteComponentProps } from 'react-router-dom';
import { useT } from 'utils/i18n';

import { GuideRender } from './components/GuideRender';
import Left from './components/Left';
import { MessageListener } from './components/MessageListener';
import SaveBarContent from './components/SaveBarContent';
import { SideEffect } from './components/SideEffect';
import View from './components/View';
import { WidgetNameEnum } from './components/Widget/const';
import { IsAttaEditorContext } from './context';
import { descriptionSetter } from './descriptions';
import styles from './ReturnPageEditor.module.scss';

export interface StateProps {
    returnPolicyContent: string | null;
}

export interface DispatchProps {}

export type RouteProps = Partial<RouteComponentProps<{}>>;

type ReturnsPageEditorProps = Pick<
    OwnProps,
    'onBack' | 'primaryAction' | 'secondaryAction' | 'title'
>;

const ReturnsPageEditorWrapper: React.FC<ReturnsPageEditorProps> = props => {
    return (
        <IsAttaEditorContext.Provider value={true}>
            <ReturnsPageEditor {...props} />
        </IsAttaEditorContext.Provider>
    );
};

const useUpdateWidgetSchema = () => {
    const { setFieldValue } = useFormikContext<PageEditorFormValues>();

    // 导入所需的 hooks
    const { updateI18nResource } = usePreviewI18nResource();
    const { sendPreviewMessage } = usePreviewMessageV2();
    const { t } = useT();

    return useCallback(
        (editor: AttaEditor, elementName: string, id: number) => {
            const getSchema = editor.elementTree.getSchema;
            if (elementName === WidgetNameEnum.HelpWidget && id) {
                const schema = getSchema(id);
                setFieldValue('supportChatWidgetSchema', schema || null);
            } else if (elementName === WidgetNameEnum.TrackingWidget && id) {
                const schema = getSchema(id);
                setFieldValue('returnTrackingPageWidgetSchema', schema || null);

                // 如果 schema 为空（widget 被删除），恢复默认文案并关闭 shopper 侧 widget
                if (!schema) {
                    const trackingWidgetTextKey =
                        'page.widget.trackingWidget.buttonText';
                    const defaultText = t(trackingWidgetTextKey);
                    updateI18nResource(trackingWidgetTextKey, defaultText);
                    sendPreviewMessage(EventName.UpdatePreviewData, {
                        type: 'UpdateTrackingWidget' as any,
                        payload: {
                            button_text: defaultText,
                            tracking_page_id: '',
                            enabled: false,
                        },
                    });
                }
            }
        },
        [setFieldValue, updateI18nResource, sendPreviewMessage, t]
    );
};

const ReturnsPageEditor: React.FC<ReturnsPageEditorProps> = ({
    primaryAction,
    secondaryAction,
    onBack,
    title,
}) => {
    const { client, selectGroupSection } = usePageEditorContext();

    const { data } = useQueryPageUrls('returns');

    const { sendPreviewMessage: sendPreviewMessageV2 } = usePreviewMessageV2();

    // 切换 Desktop / Mobile 预览模式
    useEffect(() => {
        sendPreviewMessageV2(EventName.UpdateView, {
            type: UpdateViewType.ToggleMobileView,
            payload: {
                isMobile: client === Client.Mobile,
            },
        });
    }, [client, sendPreviewMessageV2]);

    const [editor, setEditor] = useState<AttaEditor | null>(null);

    const useI18n = (key: string) => {
        const { t } = useT();
        return t(key);
    };

    const updateWidgetSchema = useUpdateWidgetSchema();

    useEffect(() => {
        if (!data) return;
        const { pageEditorUrl } = generateReturnPageUrl(data.priorityUrl);
        const viewPortURL = `${pageEditorUrl}&atta_editor=true`;

        const editor = new AttaEditor({
            viewPortURL,
            settingComponentLoader: () => undefined,
            useI18n,
            renders: {
                guideRender: GuideRender,
                interactionRender: () => null,
            },
        });

        editor.event.on('ElementPropSet', e => {
            updateWidgetSchema(editor, e.element.name, e.element.id);
        });
        editor.event.on('ElementAdded', e => {
            updateWidgetSchema(editor, e.element.name, e.id);
        });
        editor.event.on('ElementRemoved', e => {
            updateWidgetSchema(editor, e.element.name, e.element.id);
        });

        descriptionSetter(editor);

        setEditor(editor);

        // 接收预览页面消息
        const remove = messageChannel.on<{ name: string }>(
            EventType.ON_PREVIEW_SECTION_ACTIVE,
            message => {
                const { section, group } = previewSectionNameMap[message.name];
                section && selectGroupSection(section, group);
            }
        );

        return () => {
            editor?.dispose();
            remove();
        };
    }, [selectGroupSection, data, updateWidgetSchema]);

    return (
        <div className={styles.editor}>
            {editor && (
                <AttaEditorProvider editor={editor}>
                    <SideEffect />
                    <MessageListener />
                    <FullScreenWorkshop
                        title={title}
                        onBack={onBack}
                        primaryAction={primaryAction}
                        secondaryAction={secondaryAction}
                        savebarContent={<SaveBarContent />}
                        editor={<Left />}
                        preview={<View />}
                    />
                </AttaEditorProvider>
            )}
        </div>
    );
};

export default ReturnsPageEditorWrapper;
